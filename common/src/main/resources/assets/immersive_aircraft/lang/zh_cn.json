{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Aircraft", "key.immersive_aircraft.multi_control_left": "向左移动", "key.immersive_aircraft.multi_control_right": "向右移动", "key.immersive_aircraft.multi_control_forward": "向前移动", "key.immersive_aircraft.multi_control_backward": "向后移动", "key.immersive_aircraft.multi_control_up": "向上移动", "key.immersive_aircraft.multi_control_down": "向下移动", "key.immersive_aircraft.multi_control_pull": "拉起控制杆", "key.immersive_aircraft.multi_control_push": "推动控制杆", "key.immersive_aircraft.multi_use": "使用武器/坐骑", "key.immersive_aircraft.fallback_control_left": "向左移动", "key.immersive_aircraft.fallback_control_right": "向右移动", "key.immersive_aircraft.fallback_control_forward": "向前移动", "key.immersive_aircraft.fallback_control_backward": "向后移动", "key.immersive_aircraft.fallback_control_up": "向上移动", "key.immersive_aircraft.fallback_control_down": "向下移动", "key.immersive_aircraft.fallback_control_pull": "拉起控制杆", "key.immersive_aircraft.fallback_control_push": "推动控制杆", "key.immersive_aircraft.fallback_use": "使用武器/坐骑", "key.immersive_aircraft.dismount": "离开载具", "key.immersive_aircraft.boost": "火箭助推", "entity.immersive_aircraft.airship": "飞艇", "entity.immersive_aircraft.cargo_airship": "货运飞船", "entity.immersive_aircraft.warship": "战斗飞艇", "entity.immersive_aircraft.biplane": "双翼机", "entity.immersive_aircraft.gyrodyne": "固定旋翼机", "entity.immersive_aircraft.quadrocopter": "四轴飞行器", "item.immersive_aircraft.hull": "机身", "item.immersive_aircraft.engine": "发动机", "item.immersive_aircraft.sail": "风帆", "item.immersive_aircraft.propeller": "螺旋桨", "item.immersive_aircraft.boiler": "锅炉", "item.immersive_aircraft.enhanced_propeller": "增强型螺旋桨", "item.immersive_aircraft.steel_boiler": "钢制锅炉", "item.immersive_aircraft.industrial_gears": "工业齿轮", "item.immersive_aircraft.sturdy_pipes": "加固管道", "item.immersive_aircraft.gyroscope": "陀螺仪", "item.immersive_aircraft.hull_reinforcement": "机身加固板", "item.immersive_aircraft.improved_landing_gear": "改良起落架", "item.immersive_aircraft.rotary_cannon": "旋转炮", "item.immersive_aircraft.bomb_bay": "炸弹舱", "item.immersive_aircraft.telescope": "望远镜", "item.immersive_aircraft.heavy_crossbow": "重弩", "item.immersive_aircraft.rotary_cannon.description": "使用火药作为弹药的速射炮。", "item.immersive_aircraft.bomb_bay.description": "投掷 TNT 炸药，不会摧毁方块，但会造成大量伤害。", "item.immersive_aircraft.telescope.description": "体积更大的望远镜。", "item.immersive_aircraft.heavy_crossbow.description": "重型弩，具有强大的冲击力，需要箭矢。", "item.immersive_aircraft.item.upgrade": "飞机升级", "item.immersive_aircraft.item.weapon": "飞机武器", "item.immersive_aircraft.airship": "飞艇", "item.immersive_aircraft.cargo_airship": "货运飞船", "item.immersive_aircraft.warship": "战斗飞艇", "item.immersive_aircraft.biplane": "双翼机", "item.immersive_aircraft.gyrodyne": "固定旋翼机", "item.immersive_aircraft.quadrocopter": "四轴飞行器", "item.immersive_aircraft.airship.description": "飞艇可能飞得不快，但驾驶起来很轻松。", "item.immersive_aircraft.cargo_airship.description": "速度慢，耗油量大，但能运载整个仓库。", "item.immersive_aircraft.warship.description": "飞行堡垒，速度缓慢但全副武装。", "item.immersive_aircraft.biplane.description": "速度快，相当可靠。请确保您的跑道足够长。", "item.immersive_aircraft.gyrodyne.description": "直升机，但是纯人力发动。使劲一推，你就能上天！", "item.immersive_aircraft.quadrocopter.description": "四个螺旋桨绑在一个竹架上，这是一个工程的杰作！适合用于建造，仅此而已。", "immersive_aircraft.gyrodyne_target": "推力到达%d%%，再加把劲！", "immersive_aircraft.gyrodyne_target_reached": "已满足螺旋桨最低速度要求，准备起飞！", "immersive_aircraft.invalid_dimension": "这架飞机在这个维度上不起作用。", "immersive_aircraft.out_of_ammo": "没子弹了", "immersive_aircraft.repair": "%s%%已修复！", "immersive_aircraft.tried_dismount": "再按一次跳出！", "immersive_aircraft.fuel.none": "无燃料！", "immersive_aircraft.fuel.out": "燃料耗尽！", "immersive_aircraft.fuel.low": "燃料低！", "immersive_aircraft.fat.none": "无食物！", "immersive_aircraft.fat.out": "你太饿了，无法进行飞行！", "option.immersive_aircraft.general": "通用设置", "option.immersive_aircraft.separateCamera": "在飞机上设置单独的视角", "option.immersive_aircraft.useThirdPersonByDefault": "在飞机上默认为第三人称视角", "option.immersive_aircraft.enableTrails": "华丽飞机尾流", "option.immersive_aircraft.enableAnimatedSails": "风帆飘扬效果", "option.immersive_aircraft.renderDistance": "渲染距离（单位为方块）", "option.immersive_aircraft.fuelConsumption": "燃料燃烧率", "option.immersive_aircraft.windClearWeather": "晴朗天气时的风效应", "option.immersive_aircraft.windRainWeather": "雨天时的风效应", "option.immersive_aircraft.windThunderWeather": "雷雨时的风效应", "option.immersive_aircraft.repairSpeed": "每次点击修复的耐久", "option.immersive_aircraft.repairExhaustion": "每次点击都会耗尽玩家的体力。", "option.immersive_aircraft.collisionDamage": "碰撞伤害", "option.immersive_aircraft.burnFuelInCreative": "在创造模式中燃烧燃料", "option.immersive_aircraft.acceptVanillaFuel": "接受原生燃料。", "option.immersive_aircraft.useCustomKeybindSystem": "使用多按键绑定可能导致某些模组出现问题。", "option.immersive_aircraft.showHotbarEngineGauge": "在快捷栏上渲染引擎仪表", "option.immersive_aircraft.enableCrashExplosion": "启用爆炸。", "option.immersive_aircraft.enableCrashBlockDestruction": "爆炸可摧毁方块", "option.immersive_aircraft.enableCrashFire": "爆炸生成火焰", "option.immersive_aircraft.crashExplosionRadius": "碰撞爆炸的威力", "option.immersive_aircraft.crashDamage": "碰撞时对玩家造成的伤害", "option.immersive_aircraft.preventKillThroughCrash": "载具坠毁时不会杀死玩家", "option.immersive_aircraft.healthBarRow": "载具生命值条竖直偏移量", "option.immersive_aircraft.damagePerHealthPoint": "载具耐久（数值越高飞机抗打击越强）", "option.immersive_aircraft.weaponsAreDestructive": "允许某些武器摧毁方块。", "option.immersive_aircraft.dropInventory": "载具摧毁时，掉落物品栏物品，而非保存在载具中", "option.immersive_aircraft.dropUpgrades": "载具摧毁时，掉落载具装备，而非保存在载具中", "option.immersive_aircraft.regenerateHealthEveryNTicks": "模拟原版行为，每隔一定时间自动恢复健康。", "option.immersive_aircraft.requireShiftForRepair": "只有在换挡时才能进行维修，否则只需进入车辆即可。", "immersive_aircraft.tooltip.no_target": "需要在地板上组装！", "immersive_aircraft.tooltip.no_space": "空间不够", "immersive_aircraft.slot.booster": "助推火箭槽", "immersive_aircraft.slot.weapon": "武器插槽", "immersive_aircraft.slot.boiler": "燃料槽", "immersive_aircraft.slot.banner": "旗帜槽", "immersive_aircraft.slot.dye": "染料槽", "immersive_aircraft.slot.upgrade": "升级槽", "immersive_aircraft.upgrade.enginespeed": "%s%% 发动机功率", "immersive_aircraft.upgrade.friction": "%s%% 空气摩擦", "immersive_aircraft.upgrade.acceleration": "%s%% 起飞速度", "immersive_aircraft.upgrade.durability": "%s%% 耐久度", "immersive_aircraft.upgrade.fuel": "%s%% 燃料消耗", "immersive_aircraft.upgrade.wind": "%s%% 风的影响", "immersive_aircraft.upgrade.stabilizer": "%s%%稳定", "immersive_aircraft.tooltip.inventory": "包含 %s 件物品。"}
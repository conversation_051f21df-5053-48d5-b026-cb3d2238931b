package immersive_aircraft.client.render.entity.renderer;

import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import immersive_aircraft.Main;
import immersive_aircraft.entity.misc.Trail;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.phys.Vec3;
import org.joml.Matrix3f;
import org.joml.Vector3f;

public class TrailRenderer {
    private static final ResourceLocation identifier = Main.locate("textures/entity/trail.png");

    public static void render(Trail trail, MultiBufferSource vertexConsumerProvider, PoseStack.Pose matrices) {
        render(trail, vertexConsumerProvider, matrices, null);
    }

    public static void render(Trail trail, MultiBufferSource vertexConsumerProvider, PoseStack.Pose matrices, float[] customColor) {
        if (trail.nullEntries >= trail.size || trail.entries == 0) {
            return;
        }

        VertexConsumer lineVertexConsumer = vertexConsumerProvider.getBuffer(RenderType.beaconBeam(identifier, true));
        int light = 15728640;

        Vec3 pos = Minecraft.getInstance().gameRenderer.getMainCamera().getPosition();
        Matrix3f matrix = matrices.normal();

        // Use different rendering methods for colored vs default trails
        if (customColor != null && customColor.length >= 3) {
            renderColoredTrail(trail, lineVertexConsumer, matrix, pos, light, customColor);
        } else {
            renderDefaultTrail(trail, lineVertexConsumer, matrix, pos, light);
        }
    }

    private static void renderColoredTrail(Trail trail, VertexConsumer lineVertexConsumer, Matrix3f matrix, Vec3 pos, int light, float[] customColor) {
        // Render multiple layers for thick parade-style trails
        float[] offsets = {0.0f, 0.4f, 0.8f, 1.2f, 1.6f}; // Multiple layers for thickness
        float[] alphaMultipliers = {1.0f, 0.8f, 0.6f, 0.4f, 0.2f}; // Decreasing opacity for outer layers

        // Reduce color intensity for more natural appearance
        float colorReduction = 0.7f; // Reduce intensity to 70% of original
        float[] reducedColor = {
            customColor[0] * colorReduction,
            customColor[1] * colorReduction,
            customColor[2] * colorReduction
        };

        for (int layer = 0; layer < offsets.length; layer++) {
            float offset = offsets[layer];
            float alphaMult = alphaMultipliers[layer];

            for (int i = 1; i < Math.min(trail.entries, trail.size); i++) {
                int pre = ((i + trail.lastIndex - 1) % trail.size) * 7;
                int index = ((i + trail.lastIndex) % trail.size) * 7;

                int a1 = (int) ((1.0f - ((float) i) / trail.size * 255) * trail.buffer[pre + 6] * alphaMult);
                int a2 = i == (trail.size - 1) ? 0 : (int) ((1.0f - ((float) i + 1) / trail.size * 255) * trail.buffer[index + 6] * alphaMult);

                vertexWithOffset(trail, lineVertexConsumer, matrix, 0, 0, pre, pos, a1, light, reducedColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, reducedColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, reducedColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 1, 0, index, pos, a2, light, reducedColor, offset);

                // Anti-culling
                vertexWithOffset(trail, lineVertexConsumer, matrix, 1, 0, index, pos, a2, light, reducedColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, reducedColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, reducedColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 0, 0, pre, pos, a1, light, reducedColor, offset);
            }
        }
    }

    private static void renderDefaultTrail(Trail trail, VertexConsumer lineVertexConsumer, Matrix3f matrix, Vec3 pos, int light) {
        for (int i = 1; i < Math.min(trail.entries, trail.size); i++) {
            int pre = ((i + trail.lastIndex - 1) % trail.size) * 7;
            int index = ((i + trail.lastIndex) % trail.size) * 7;

            int a1 = (int) ((1.0f - ((float) i) / trail.size * 255) * trail.buffer[pre + 6]);
            int a2 = i == (trail.size - 1) ? 0 : (int) ((1.0f - ((float) i + 1) / trail.size * 255) * trail.buffer[index + 6]);

            vertex(trail, lineVertexConsumer, matrix, 0, 0, pre, pos, a1, light, null);
            vertex(trail, lineVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, null);
            vertex(trail, lineVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, null);
            vertex(trail, lineVertexConsumer, matrix, 1, 0, index, pos, a2, light, null);

            // Anti-culling
            vertex(trail, lineVertexConsumer, matrix, 1, 0, index, pos, a2, light, null);
            vertex(trail, lineVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, null);
            vertex(trail, lineVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, null);
            vertex(trail, lineVertexConsumer, matrix, 0, 0, pre, pos, a1, light, null);
        }
    }

    private static void vertex(Trail trail, VertexConsumer lineVertexConsumer, Matrix3f matrix, float u, float v, int index, Vec3 pos, float a, int light, float[] customColor) {
        Vector3f p = new Vector3f((float) (trail.buffer[index] - pos.x), (float) (trail.buffer[index + 1] - pos.y), (float) (trail.buffer[index + 2] - pos.z));
        matrix.transform(p);

        // Use custom color if provided, otherwise use default gray
        if (customColor != null && customColor.length >= 3) {
            lineVertexConsumer.vertex(p.x, p.y, p.z, customColor[0], customColor[1], customColor[2], a, u, v, OverlayTexture.NO_OVERLAY, light, 1, 0, 0);
        } else {
            lineVertexConsumer.vertex(p.x, p.y, p.z, trail.gray, trail.gray, trail.gray, a, u, v, OverlayTexture.NO_OVERLAY, light, 1, 0, 0);
        }
    }

    private static void vertexWithOffset(Trail trail, VertexConsumer lineVertexConsumer, Matrix3f matrix, float u, float v, int index, Vec3 pos, float a, int light, float[] customColor, float offset) {
        Vector3f p = new Vector3f((float) (trail.buffer[index] - pos.x), (float) (trail.buffer[index + 1] - pos.y), (float) (trail.buffer[index + 2] - pos.z));

        // Create expanded trail effect for colored trails - multiple layers with offsets
        if (offset > 0.0f) {
            // Create perpendicular expansion for thickness
            float angle = (float) (index * 0.1f); // Slight rotation for natural look
            float expandX = (float) (Math.cos(angle) * offset);
            float expandZ = (float) (Math.sin(angle) * offset);
            p.add(expandX, offset * 0.3f, expandZ); // Slight vertical component too
        }

        matrix.transform(p);

        // Use the provided custom color (already reduced in intensity)
        lineVertexConsumer.vertex(p.x, p.y, p.z, customColor[0], customColor[1], customColor[2], a, u, v, OverlayTexture.NO_OVERLAY, light, 1, 0, 0);
    }


}

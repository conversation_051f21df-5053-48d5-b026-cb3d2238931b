package immersive_aircraft.entity.weapon;

import immersive_aircraft.entity.VehicleEntity;
import immersive_aircraft.entity.InventoryVehicleEntity;
import immersive_aircraft.entity.misc.WeaponMount;
import immersive_aircraft.cobalt.network.NetworkHandler;
import immersive_aircraft.network.c2s.FireMessage;
import net.minecraft.core.registries.BuiltInRegistries;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.Tag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;

import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.entity.projectile.Arrow;
import net.minecraft.world.item.ItemStack;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.item.ProjectileWeaponItem;
import net.minecraft.world.phys.Vec3;
import org.joml.Matrix3f;
import org.joml.Vector3f;
import org.joml.Vector4f;
import rbasamoyai.createbigcannons.munitions.FuzedProjectileBlockItem;
import rbasamoyai.createbigcannons.munitions.big_cannon.ProjectileBlock;
import rbasamoyai.createbigcannons.munitions.big_cannon.ProjectileBlockItem;
import rbasamoyai.createbigcannons.munitions.config.MunitionPropertiesHandler;
import rbasamoyai.createbigcannons.munitions.config.PropertiesTypeHandler;
import rbasamoyai.createbigcannons.utils.CBCRegistryUtils;

import java.util.Map;
import java.util.Set;

public class CannonShellDropper extends Weapon {
    private static final float MAX_COOLDOWN = 2.0f;
    private float cooldown = 0.0f;
    private int ammo = 0;
    private ItemStack ammoStack;

    // Hardcoded shell ammunition for Create: Advanced Technologies and other mods
    private static final Map<String, String> SHELL_TO_ENTITY_MAP;

    static {
        Map<String, String> map = new java.util.HashMap<>();

        // Create Big Cannons shells
        map.put("createbigcannons:ap_shell", "createbigcannons:ap_shell");
        map.put("createbigcannons:he_shell", "createbigcannons:he_shell");
        map.put("createbigcannons:shrapnel_shell", "createbigcannons:shrapnel_shell");
        map.put("createbigcannons:smoke_shell", "createbigcannons:smoke_shell");
        map.put("createbigcannons:gas_shell", "createbigcannons:gas_shell");
        map.put("createbigcannons:fluid_shell", "createbigcannons:fluid_shell");

        // Create: Advanced Technologies shells (cbc_at) - spawn base CBC projectiles
        map.put("cbc_at:ap_shell", "createbigcannons:ap_shell");
        map.put("cbc_at:he_shell", "createbigcannons:he_shell");
        map.put("cbc_at:shrapnel_shell", "createbigcannons:shrapnel_shell");
        map.put("cbc_at:smoke_shell", "createbigcannons:smoke_shell");
        map.put("cbc_at:gas_shell", "createbigcannons:gas_shell");
        map.put("cbc_at:fluid_shell", "createbigcannons:fluid_shell");
        map.put("cbc_at:cluster", "cbc_at:cluster_projectile");

        // Create AT shells with cartridge at the back (spawn base CBC projectiles)
        map.put("cbc_at:armor_piercing_cartridge", "createbigcannons:ap_shell");
        map.put("cbc_at:high_explosive_cartridge", "createbigcannons:he_shell");
        map.put("cbc_at:shrapnel_cartridge", "createbigcannons:shrapnel_shell");
        map.put("cbc_at:smoke_cartridge", "createbigcannons:smoke_shell");
        map.put("cbc_at:gas_cartridge", "createbigcannons:gas_shell");
        map.put("cbc_at:fluid_cartridge", "createbigcannons:fluid_shell");
        map.put("cbc_at:cluster_cartridge", "cbc_at:cluster_projectile");

        // Create AT shells with caseless at the back (spawn base CBC projectiles)
        map.put("cbc_at:armor_piercing_caseless", "createbigcannons:ap_shell");
        map.put("cbc_at:high_explosive_caseless", "createbigcannons:he_shell");
        map.put("cbc_at:shrapnel_caseless", "createbigcannons:shrapnel_shell");
        map.put("cbc_at:smoke_caseless", "createbigcannons:smoke_shell");
        map.put("cbc_at:gas_caseless", "createbigcannons:gas_shell");
        map.put("cbc_at:fluid_caseless", "createbigcannons:fluid_shell");
        map.put("cbc_at:cluster_caseless", "cbc_at:cluster_projectile");



        // Cannon Nukes
        map.put("canonnukes:nuke_shell", "canonnukes:nuke_shell_projectile");

        // Fallback items
        map.put("minecraft:tnt", "minecraft:tnt");
        map.put("minecraft:fire_charge", "minecraft:fire_charge");

        SHELL_TO_ENTITY_MAP = java.util.Collections.unmodifiableMap(map);
    }

    private static final java.util.Set<String> FALLBACK_AMMUNITION = java.util.Set.of(
            "minecraft:tnt",
            "minecraft:fire_charge"
    );

    public CannonShellDropper(VehicleEntity entity, ItemStack stack, WeaponMount mount, int slot) {
        super(entity, stack, mount, slot);
    }

    @Override
    public void tick() {
        cooldown -= 1.0f / 20.0f;
    }

    @Override
    public void fire(Vector3f direction) {
        if (spentAmmo(1)) {
            dropCannonShell(direction);
        }
    }

    @Override
    public void clientFire(int index) {
        if (cooldown <= 0.0f) {
            cooldown = MAX_COOLDOWN;
            NetworkHandler.sendToServer(new FireMessage(getSlot(), index, getDirection()));
        }

    }
    private void dropCannonShell(Vector3f direction) {
        // Calculate the drop position
        Vector4f position = getDropOffset();
        VehicleEntity entity = getEntity();
        position.mul(getMount().transform());
        position.mul(entity.getVehicleTransform());

        // Try to spawn Create Big Cannon shell entity
        if (ammoStack != null && !ammoStack.isEmpty()) {
            Entity shellEntity = createCannonShellEntity(entity, ammoStack, position, direction);

            if (shellEntity != null) {
                entity.level().addFreshEntity(shellEntity);
            }
        }

        // Play sound
        getEntity().playSound(SoundEvents.DISPENSER_DISPENSE, 1.0f, 0.8f + 0.4f * entity.level().random.nextFloat());
    }





    private Entity createCannonShellEntity(VehicleEntity vehicle, ItemStack ammoStack, Vector4f position, Vector3f direction) {
        String itemId = BuiltInRegistries.ITEM.getKey(ammoStack.getItem()).toString();
//        System.out.println("[CannonShellDropper] Creating shell entity for: " + itemId);

        // Try hardcoded mapping first
        if (SHELL_TO_ENTITY_MAP.containsKey(itemId)) {
            String entityId = SHELL_TO_ENTITY_MAP.get(itemId);
//            System.out.println("[CannonShellDropper] Using hardcoded mapping: " + itemId + " -> " + entityId);

            Entity shellEntity = createShellEntity(entityId, vehicle, position, direction);
            if (shellEntity != null) {
//                System.out.println("[CannonShellDropper] Successfully created shell entity: " + shellEntity.getClass().getSimpleName());
                return shellEntity;
            } else {
//                System.out.println("[CannonShellDropper] Failed to create entity from hardcoded mapping: " + entityId);
            }
        }

        // Try auto-detection as backup
        Entity shellEntity = createShellFromBigCannonMunition(ammoStack, vehicle, position, direction);

        if (shellEntity == null) {
//            System.out.println("[CannonShellDropper] All methods failed, falling back to arrow for: " + itemId);
            // Fallback: create a simple arrow projectile for vanilla items
            Arrow arrow = new Arrow(vehicle.level(), position.x(), position.y(), position.z());
            arrow.setOwner(vehicle.getControllingPassenger());
            // Inherit aircraft velocity exactly
            arrow.setDeltaMovement(vehicle.getDeltaMovement());
            shellEntity = arrow;
        } else {
//            System.out.println("[CannonShellDropper] Successfully created shell entity: " + shellEntity.getClass().getSimpleName());
        }

        return shellEntity;
    }

    /**
     * Creates a shell entity from a BigCannonMunitionBlock item
     */
    private Entity createShellFromBigCannonMunition(ItemStack ammoStack, VehicleEntity vehicle, Vector4f position, Vector3f direction) {
        try {
            // Check if the item is an instance of BigCannonMunitionBlock
            Class<?> bigCannonMunitionBlockClass = Class.forName("rbasamoyai.createbigcannons.munitions.big_cannon.BigCannonMunitionBlock");

            String itemId = BuiltInRegistries.ITEM.getKey(ammoStack.getItem()).toString();
//            System.out.println("[CannonShellDropper] Checking if item is BigCannonMunitionBlock: " + itemId);

            if (bigCannonMunitionBlockClass.isInstance(ammoStack.getItem())) {
//                System.out.println("[CannonShellDropper] Item is BigCannonMunitionBlock: " + itemId);

                // Try to create the corresponding projectile entity
                Entity shellEntity = createShellEntityFromMunitionBlock(ammoStack, vehicle, position, direction);
                if (shellEntity != null) {
                    return shellEntity;
                }
            } else {
//                System.out.println("[CannonShellDropper] Item is not BigCannonMunitionBlock: " + itemId);
            }

            return null;

        } catch (ClassNotFoundException e) {
            System.out.println("[CannonShellDropper] Create Big Cannons not available: " + e.getMessage());
            return null;
        } catch (Exception e) {
            System.out.println("[CannonShellDropper] Error checking BigCannonMunitionBlock: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Creates a shell entity from a munition block using various patterns
     */
    private Entity createShellEntityFromMunitionBlock(ItemStack ammoStack, VehicleEntity vehicle, Vector4f position, Vector3f direction) {
        String itemId = BuiltInRegistries.ITEM.getKey(ammoStack.getItem()).toString();

        // Try different entity naming patterns
        String[] entityPatterns = {
            itemId,  // Direct match (e.g., createbigcannons:he_shell)
            itemId + "_projectile",  // Pattern: item_projectile
            itemId.replace("_shell", "_projectile"),  // Pattern: replace _shell with _projectile
            itemId.replace("_shell", ""),  // Pattern: remove _shell
        };

        for (String entityId : entityPatterns) {
//            System.out.println("[CannonShellDropper] Trying entity pattern: " + entityId);
            Entity shell = createShellEntity(entityId, vehicle, position, direction);
            if (shell != null) {
//                System.out.println("[CannonShellDropper] Successfully created entity with pattern: " + entityId);
                return shell;
            }
        }

//        System.out.println("[CannonShellDropper] No entity patterns worked for: " + itemId);
        return null;
    }

    private Entity createShellEntity(String entityId, VehicleEntity vehicle, Vector4f position, Vector3f direction) {
        try {
            ResourceLocation entityLocation = ResourceLocation.tryParse(entityId);
            if (entityLocation != null && BuiltInRegistries.ENTITY_TYPE.containsKey(entityLocation)) {
                EntityType<?> entityType = BuiltInRegistries.ENTITY_TYPE.get(entityLocation);
                Entity shell = entityType.create(vehicle.level());
//                System.out.println(shell.getUUID());
                if (shell != null) {
                    setupShellProperties(shell, ammoStack);
                    shell.setPos(position.x(), position.y()-2, position.z());

                    // Set velocity - inherit aircraft velocity exactly
                    Vec3 aircraftVelocity = vehicle.getSpeedVector();
                    shell.setDeltaMovement(aircraftVelocity);
                    // Set owner if it's a projectile
                    if (shell instanceof AbstractArrow arrow && vehicle.getControllingPassenger() != null) {
                        arrow.setOwner(vehicle.getControllingPassenger());
                    }

                    // Set up shell properties using NBT data


                    return shell;
                }
            }
        } catch (Exception e) {
            // If Create Big Cannons entity creation fails, return null to use fallback
        }

        return null;
    }

    private void setupShellProperties(Entity shell, ItemStack ammoStack) {

        try {
            CompoundTag shellNBT = new CompoundTag();

            CompoundTag itemNBT = ammoStack.getTag();

            // Copy fuse data from the item to the shell entity

            if (itemNBT != null) {
                if (itemNBT.contains("BlockEntityTag") && itemNBT.get("BlockEntityTag") instanceof CompoundTag) {
                    for (String key : ((CompoundTag) itemNBT.get("BlockEntityTag")).getAllKeys()) {
//                        System.out.println("[CannonShellDropper] Copying NBT key: " + key);
                        if (key.equals("FluidContent")) {
                            CompoundTag fluidContent = ((CompoundTag) itemNBT.get("BlockEntityTag")).getCompound(key);
                            CompoundTag innerFluidTag = new CompoundTag();
                            CompoundTag fluidTag = new CompoundTag();
                            if (fluidContent.contains("FluidName")) {
                                fluidTag.putString("Fluid", fluidContent.getString("FluidName"));
                            }

                            if (fluidContent.contains("Amount")) {
                                fluidTag.putInt("FluidAmount", fluidContent.getInt("Amount"));
                            }

                            fluidTag.put("FluidTag", innerFluidTag);
                            shellNBT.put("Fluid", fluidTag);
                        }

                        // Copy over fuze if present
                        else {
                            shellNBT.put(key, ((CompoundTag) itemNBT.get("BlockEntityTag")).get(key).copy());
                        }
                    }
                }
                shellNBT.putBoolean("HasBeenShot", true);
            }
//            shell.saveWithoutId(shellNBT);
            shell.load(shellNBT);
        } catch (Exception e) {
            e.printStackTrace();
            // If NBT setup fails, shell will still work with default properties
        }
    }



    private Vector4f getDropOffset() {
        return new Vector4f(0.0f, -1.0f, 0.0f, 1.0f);
    }

    private Vector3f getDirection() {
        Vector3f direction = new Vector3f(0, -1.0f, 0);
        direction.mul(new Matrix3f(getMount().transform()));
        direction.mul(getEntity().getVehicleNormalTransform());
        return direction;
    }

    protected boolean spentAmmo(int amount) {
        if (ammo < amount && getEntity() instanceof InventoryVehicleEntity vehicle) {
            for (int i = 0; i < vehicle.getInventory().getContainerSize(); i++) {
                ItemStack stack = vehicle.getInventory().getItem(i);
                if (isCannonShell(stack)) {
                    ammoStack = stack.copy();
                    ammoStack.setCount(1); // Only drop one shell at a time

                    if (!getEntity().isPilotCreative()) {
                        ammo += 1; // Each shell counts as 1 ammo
                        stack.shrink(1);
                    }
                    break;
                }
            }
        }

        if (getEntity().isPilotCreative()) {
            // In creative mode, create a default shell if no ammo stack exists
            if (ammoStack == null || ammoStack.isEmpty()) {
                ammoStack = createDefaultShell();
            }
            return ammoStack != null && !ammoStack.isEmpty();
        }

        if (ammo <= 0) {
            if (getEntity().getControllingPassenger() instanceof Player player) {
                player.displayClientMessage(Component.translatable("immersive_aircraft.out_of_ammo"), true);
            }
            return false;
        }

        ammo -= amount;
        return true;
    }

    /**
     * Checks if an item is a cannon shell - hardcoded for Create: Advanced Technologies
     */
    private boolean isCannonShell(ItemStack stack) {
        if (stack.isEmpty()) {
            return false;
        }

        String itemId = BuiltInRegistries.ITEM.getKey(stack.getItem()).toString();
//        System.out.println("[CannonShellDropper] Checking if item is cannon shell: " + itemId);

        // Check hardcoded shell map
        if (SHELL_TO_ENTITY_MAP.containsKey(itemId)) {
//            System.out.println("[CannonShellDropper] Item is in hardcoded shell map: " + itemId);
            return true;
        }

        // Check if it's a BigCannonMunitionBlock (backup)
        if (isBigCannonMunitionBlock(stack)) {
//            System.out.println("[CannonShellDropper] Item is BigCannonMunitionBlock: " + itemId);
            return true;
        }

        // Check if it has Create Big Cannon shell NBT structure (backup)
        if (hasCannonShellNBT(stack)) {
//            System.out.println("[CannonShellDropper] Item has cannon shell NBT: " + itemId);
            return true;
        }

//        System.out.println("[CannonShellDropper] Item is not a cannon shell: " + itemId);
        return false;
    }

    /**
     * Checks if the item is an instance of BigCannonMunitionBlock
     */
    private boolean isBigCannonMunitionBlock(ItemStack stack) {
        try {
            Class<?> bigCannonMunitionBlockClass = Class.forName("rbasamoyai.createbigcannons.munitions.big_cannon.BigCannonMunitionBlock");
            return bigCannonMunitionBlockClass.isInstance(stack.getItem());
        } catch (ClassNotFoundException e) {
            // Create Big Cannons not available
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Checks if the item has the characteristic NBT structure of a Create Big Cannon shell
     */
    private boolean hasCannonShellNBT(ItemStack stack) {
        CompoundTag itemNBT = stack.getTag();
        if (itemNBT != null && itemNBT.contains("BlockEntityTag")) {
            CompoundTag blockEntityTag = itemNBT.getCompound("BlockEntityTag");
            // Create Big Cannon shells typically have a Fuze tag in their BlockEntityTag
            return blockEntityTag.contains("Fuze");
        }
        return false;
    }

    /**
     * Creates a default shell for creative mode
     */
    private ItemStack createDefaultShell() {
        // Try to create a Create Big Cannon HE shell if available
        try {
            ResourceLocation heShellId = ResourceLocation.tryParse("createbigcannons:he_shell");
            if (heShellId != null && BuiltInRegistries.ITEM.containsKey(heShellId)) {
                return new ItemStack(BuiltInRegistries.ITEM.get(heShellId));
            }
        } catch (Exception e) {
            // Fall through to fallback
        }

        // Fallback to TNT
        return new ItemStack(BuiltInRegistries.ITEM.get(ResourceLocation.tryParse("minecraft:tnt")));
    }
}

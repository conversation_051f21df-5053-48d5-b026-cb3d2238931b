package immersive_aircraft.entity;

import immersive_aircraft.config.Config;
import immersive_aircraft.item.upgrade.VehicleStat;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.level.Level;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.phys.Vec3;
import org.joml.Vector3f;

/**
 * Implements airplane like physics properties and accelerated towards
 */
public abstract class AirplaneEntity extends AircraftEntity {
    private int time;

    public AirplaneEntity(EntityType<? extends AircraftEntity> entityType, Level world, boolean canExplodeOnCrash) {
        super(entityType, world, canExplodeOnCrash);
        time = 0;
    }

    @Override
    protected boolean useAirplaneControls() {
        return true;
    }

    @Override
    protected float getGravity() {
        Vector3f direction = getForwardDirection();
        float speed = (float) getDeltaMovement().length() * (1.0f - Math.abs(direction.y));
        if (isNearGround() && speed > 0.0) {
            time++;
        }
//        System.out.println(time);

        // Calculate if aircraft is in stall condition
        float mass = getProperties().get(VehicleStat.MASS);
        float lift = getProperties().get(VehicleStat.LIFT);
        float minSpeed = (mass / (lift * 10.0f)) * 0.15f;
        boolean isStalling = !isNearGround() && speed < minSpeed;

        // Reduce lift factor during stall
        float liftFactor = isStalling ? 0.3f : 1.5f; // Much less lift when stalling
        float extraGravity = 0;
        if (isStalling) {
            extraGravity = 0.5f;
        }
        if (isNearGround() && time >= Config.getInstance().timeRequiredForTakeOff) {

            if (time >= Config.getInstance().timeRequiredForTakeOff + 200) {
                time = 0;
            }
//            System.out.println("a");
            return Math.max(0.0f,  extraGravity + 1.0f - speed * liftFactor) * super.getGravity();
        } else if (isNearGround()) {
//            System.out.println("b");

            return Math.max(0.0f, 20.0f) * super.getGravity();
        } else {

//            System.out.println("c");
            return Math.max(0.0f,  extraGravity + 1.0f - speed * liftFactor) * super.getGravity();
        }
    }

    protected float getBrakeFactor() {
        return 0.95f;
    }

    /**
     * Check if the entity is within 1 block of the ground
     */
    private boolean isNearGround() {
        BlockPos pos = blockPosition();
        // Check blocks below the entity within 1 block distance
        for (int y = 0; y <= 2; y++) {
            BlockPos checkPos = pos.below(y);
            if (!level().getBlockState(checkPos).isAir()) {
                return true;
            }
        }
        return false;
    }

    /**
     * Apply nose-down effect when aircraft is going too slow
     */
    private void applyStallEffect() {
        if (isNearGround()) {
            return; // Don't apply stall effect near ground
        }

        float speed = (float) getDeltaMovement().length();

        // Calculate minimum speed based on aircraft properties
        float mass = getProperties().get(VehicleStat.MASS);
        float lift = getProperties().get(VehicleStat.LIFT);
        float minSpeed = (mass / (lift * 10.0f)) * 0.15f;

        // If going too slow, pitch nose down
        if (speed < minSpeed) {
            float pitchDownForce = (minSpeed - speed) * 2.0f; // Stronger effect the slower you go
            setXRot(getXRot() + pitchDownForce);
        }
    }

    @Override
    protected void updateController() {
        if (!isVehicle()) {
            return;
        }

        // Apply nose-down effect when going too slow
        applyStallEffect();

        super.updateController();

        // engine control
        if (movementY != 0) {
            // Slower engine spool down when cutting power
            float engineChangeRate = movementY > 0 ? 0.1f : 0.03f; // Slower when reducing power

            // Prevent lowering engine power while airborne (safety feature)
            if (movementY < 0 && !isNearGround()) {
                // Don't allow any engine power reduction when not near ground
                // Display warning message to pilot
                if (getControllingPassenger() instanceof net.minecraft.world.entity.player.Player player) {
                    player.displayClientMessage(
                        net.minecraft.network.chat.Component.translatable("immersive_aircraft.engine_safety_warning")
                            .withStyle(net.minecraft.ChatFormatting.RED),
                        true
                    );
                }
                // Keep engine at current power level
            } else {
                // Only allow engine changes when near ground or increasing power
                setEngineTarget(Math.max(0.0f, Math.min(1.0f, getEngineTarget() + engineChangeRate * movementY)));
            }

            if (movementY < 0) {
                setDeltaMovement(getDeltaMovement().scale(getBrakeFactor()));
            }
        }

        // get the direction
        Vector3f direction = getForwardDirection();

        // speed
        float thrust = (float) (Math.pow(getEnginePower(), 2.0) * getProperties().get(VehicleStat.ENGINE_SPEED));

        if (isNearGround() && getEngineTarget() < 1.0) {
            thrust = (float) ((getProperties().get(VehicleStat.PUSH_SPEED)) / (1.0f + (float) getDeltaMovement().length() * 5.0f) * pressingInterpolatedZ.getSmooth() * (1.0f - getEnginePower()));
        }

        // accelerate
        setDeltaMovement(getDeltaMovement().add(toVec3d(direction.mul(thrust))));
    }

    @Override
    public boolean canTurnOnEngine(Entity pilot) {
        // First check if it's a player
        if (!(pilot instanceof Player player)) {
            return false;
        }

        // Check if radial engine is installed
        if (!hasRadialEngine()) {
            // Display error message to player
            player.displayClientMessage(
                net.minecraft.network.chat.Component.translatable("immersive_aircraft.radial_engine_required")
                    .withStyle(net.minecraft.ChatFormatting.RED),
                true
            );
            return false;
        }

        return true;
    }

    @Override
    public boolean shouldRenderAtSqrDistance(double distance) {
        // Force 1000 block render distance for airplanes
        double d = 1000.0 * getViewScale();
        return distance < d * d;
    }
}

package immersive_aircraft.screen.slot;

import immersive_aircraft.entity.InventoryVehicleEntity;
import immersive_aircraft.entity.inventory.VehicleInventoryDescription;
import immersive_aircraft.item.upgrade.VehicleUpgradeRegistry;
import net.minecraft.world.Container;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.ItemStack;

public class UpgradeSlot extends Slot {

    private final InventoryVehicleEntity vehicle;
    private final int stackSize;

    public UpgradeSlot(InventoryVehicleEntity vehicle, int stackSize, Container inventory, int index, int x, int y) {
        super(inventory, index, x, y);

        this.vehicle = vehicle;
        this.stackSize = stackSize;
    }

    @Override
    public boolean mayPlace(ItemStack stack) {
        // Check if it's a registered upgrade
        boolean isRegisteredUpgrade = VehicleUpgradeRegistry.INSTANCE.hasUpgrade(stack.getItem());

        // Check if it's a radial engine from Create: TFMG
        boolean isRadialEngine = isRadialEngineItem(stack);

        // Check if it's a fuel tank
        boolean isFuelTank = isFuelTankItem(stack);

        // Allow if it's either a registered upgrade, radial engine, or fuel tank
        boolean isValidUpgrade = isRegisteredUpgrade || isRadialEngine || isFuelTank;

        // Ensure no duplicate items in upgrade slots
        boolean noDuplicates = vehicle.getSlots(VehicleInventoryDescription.UPGRADE).stream()
                .noneMatch(s -> s.getItem() == stack.getItem());

        return isValidUpgrade && noDuplicates;
    }

    /**
     * Checks if the item is a radial engine from Create: The Factory Must Grow
     */
    private boolean isRadialEngineItem(ItemStack stack) {
        String itemId = stack.getItem().toString();
        return itemId.contains("radial_engine") ||
               itemId.contains("tfmg:radial_engine") ||
               itemId.contains("create_industry:radial_engine") ||
               itemId.contains("createindustry:radial_engine");
    }

    /**
     * Checks if the item is a fuel tank upgrade
     */
    private boolean isFuelTankItem(ItemStack stack) {
        String itemId = stack.getItem().toString();
        return itemId.contains("fuel_tank") ||
               itemId.contains("gas_tank") ||
               itemId.contains("petrol_tank") ||
               itemId.contains("aviation_fuel_tank") ||
               itemId.contains("create:fluid_tank") ||
               itemId.contains("tfmg:fuel_tank");
    }

    @Override
    public int getMaxStackSize() {
        return stackSize;
    }
}
